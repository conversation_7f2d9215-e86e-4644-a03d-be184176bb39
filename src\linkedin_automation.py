"""LinkedIn自动化模块
实现LinkedIn登录验证、搜索职位、筛选Easy Apply职位并进行投递的功能
"""

import time
import random
from typing import List, Dict, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    NoSuchElementException, 
    TimeoutException, 
    ElementClickInterceptedException,
    StaleElementReferenceException
)
from selenium.webdriver.chrome.options import Options
from loguru import logger
import yaml
from pathlib import Path

class LinkedInAutomation:
    """LinkedIn自动化类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化LinkedIn自动化实例
        
        Args:
            config_path: 配置文件路径
        """
        self.driver = None
        self.wait = None
        self.config = self._load_config(config_path)
        self.is_logged_in = False
        self.applied_jobs = set()  # 记录已申请的职位
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        if config_path and Path(config_path).exists():
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        
        # 默认配置
        return {
            'linkedin': {
                'email': '',
                'password': '',
                'search_keywords': ['python developer', 'software engineer'],
                'location': 'United States',
                'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
                'job_type': ['Full-time', 'Part-time', 'Contract'],
                'remote_work': ['On-site', 'Remote', 'Hybrid'],
                'max_applications_per_day': 50,
                'delay_between_applications': [30, 60],  # 秒
                'auto_answer_questions': True,
                'default_answers': {
                    'years_experience': '3',
                    'willing_to_relocate': 'Yes',
                    'authorized_to_work': 'Yes',
                    'require_sponsorship': 'No'
                }
            },
            'selenium': {
                'headless': False,
                'implicit_wait': 10,
                'page_load_timeout': 30,
                'window_size': [1920, 1080]
            }
        }
    
    def setup_driver(self, headless: bool = None) -> webdriver.Chrome:
        """设置Chrome WebDriver"""
        # 使用优化后的chrome_browser_options函数
        from src.utils.chrome_utils import chrome_browser_options
        chrome_options = chrome_browser_options()
        
        if headless is None:
            headless = self.config['selenium']['headless']
            
        if headless:
            chrome_options.add_argument('--headless')
        
        # 窗口大小
        window_size = self.config['selenium']['window_size']
        chrome_options.add_argument(f'--window-size={window_size[0]},{window_size[1]}')
        
        # 添加额外的反检测设置
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        try:
            # 使用 webdriver_manager 自动管理 ChromeDriver
            from webdriver_manager.chrome import ChromeDriverManager
            from selenium.webdriver.chrome.service import Service

            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            # 设置等待和超时
            self.driver.implicitly_wait(self.config['selenium']['implicit_wait'])
            self.driver.set_page_load_timeout(self.config['selenium']['page_load_timeout'])
            
            self.wait = WebDriverWait(self.driver, 20)
            
            logger.info("Chrome WebDriver 设置完成")
            return self.driver
        except Exception as e:
            logger.error(f"设置Chrome WebDriver失败: {str(e)}")
            raise RuntimeError(f"设置Chrome WebDriver失败: {str(e)}")
    
    def login(self, email: str = None, password: str = None) -> Dict:
        """登录LinkedIn"""
        try:
            if not email:
                email = self.config['linkedin']['email']
            if not password:
                password = self.config['linkedin']['password']
                
            if not email or not password:
                logger.error("LinkedIn邮箱或密码未配置")
                return {"success": False, "status": "邮箱或密码未配置", "requires_action": False}
            
            logger.info(f"开始登录LinkedIn... 使用邮箱: {email[:3]}****{email[-10:]}")
            try:
                logger.debug("正在访问LinkedIn登录页面...")
                self.driver.get("https://www.linkedin.com/login")
                logger.debug(f"当前URL: {self.driver.current_url}")
            except Exception as e:
                logger.error(f"无法访问LinkedIn登录页面: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn登录页面，请检查网络连接或代理设置", "requires_action": False}
            
            # 等待登录页面加载
            try:
                logger.debug("等待登录页面元素加载...")
                email_field = self.wait.until(
                    EC.presence_of_element_located((By.ID, "username"))
                )
                logger.debug("登录页面元素加载成功")
            except TimeoutException:
                logger.error("登录页面加载超时")
                logger.debug(f"当前页面标题: {self.driver.title}")
                logger.debug(f"当前页面URL: {self.driver.current_url}")
                return {"success": False, "status": "登录页面加载超时，请检查网络连接速度", "requires_action": False}
            
            # 输入邮箱
            try:
                logger.debug("正在输入邮箱...")
                email_field.clear()
                self._human_type(email_field, email)
                logger.info("邮箱输入完成")
            except Exception as e:
                logger.error(f"邮箱输入失败: {str(e)}")
                return {"success": False, "status": "邮箱输入失败，请检查邮箱格式是否正确", "requires_action": False}
            
            # 输入密码
            try:
                logger.debug("正在输入密码...")
                password_field = self.driver.find_element(By.ID, "password")
                password_field.clear()
                self._human_type(password_field, password)
                logger.info("密码输入完成")
            except Exception as e:
                logger.error(f"密码输入失败: {str(e)}")
                return {"success": False, "status": "密码输入失败，请检查密码格式是否正确", "requires_action": False}
            
            # 点击登录按钮
            try:
                logger.debug("正在点击登录按钮...")
                login_button = self.driver.find_element(By.XPATH, "//button[@type='submit']")
                login_button.click()
                logger.info("点击登录按钮完成")
            except Exception as e:
                logger.error(f"登录按钮点击失败: {str(e)}")
                return {"success": False, "status": "登录按钮点击失败，请稍后重试", "requires_action": False}
            
            # 等待登录完成，增加等待时间以确保页面加载
            logger.debug("等待登录处理...")
            time.sleep(8)  # 增加等待时间
            
            # 记录当前URL和页面标题，帮助调试
            logger.debug(f"登录后当前URL: {self.driver.current_url}")
            logger.debug(f"登录后页面标题: {self.driver.title}")
            
            # 检查是否需要验证码或其他验证
            if "challenge" in self.driver.current_url or "checkpoint/challenge" in self.driver.current_url:
                logger.warning("检测到验证码验证")
                return {"success": False, "status": "需要完成验证码验证，请在浏览器中完成验证后重试", "requires_action": True}
            
            # 检查是否需要二次验证（checkpoint）
            if "checkpoint" in self.driver.current_url:
                logger.warning("检测到二次验证，等待用户完成验证...")
                max_wait = 120  # 最长等待秒数
                poll_interval = 2  # 检查间隔
                waited = 0
                while waited < max_wait:
                    try:
                        current_url = self.driver.current_url
                        logger.debug(f"二次验证等待中，当前URL: {current_url}")
                        if ("feed" in current_url or "linkedin.com/feed" in current_url or
                            self.driver.find_elements(By.CLASS_NAME, "global-nav") or
                            self.driver.find_elements(By.CLASS_NAME, "feed-identity-module")):
                            logger.info("二次验证后检测到已登录，进入首页")
                            self.is_logged_in = True
                            return {"success": True, "status": "登录成功", "requires_action": False}
                    except Exception as e:
                        logger.warning(f"等待二次验证时页面异常: {str(e)}")
                    time.sleep(poll_interval)
                    waited += poll_interval
                logger.error("二次验证超时，未检测到登录成功")
                return {"success": False, "status": "二次验证超时，请手动检查浏览器中的登录状态", "requires_action": True}
            
            # 检查是否有错误消息
            try:
                error_elements = self.driver.find_elements(By.CLASS_NAME, "alert-content")
                for error_element in error_elements:
                    if error_element.is_displayed():
                        error_text = error_element.text
                        logger.error(f"登录错误: {error_text}")
                        if "密码" in error_text:
                            return {"success": False, "status": "密码错误，请检查密码是否正确", "requires_action": False}
                        elif "找不到" in error_text or "不存在" in error_text:
                            return {"success": False, "status": "账号不存在，请检查邮箱是否正确", "requires_action": False}
                        else:
                            return {"success": False, "status": f"登录错误: {error_text}", "requires_action": False}
            except Exception as e:
                logger.debug(f"检查错误消息时发生异常: {str(e)}")
                pass
            
            # 验证登录状态
            try:
                # 等待页面加载完成
                logger.debug("验证登录状态...")
                try:
                    self.wait.until(
                        EC.presence_of_element_located((By.CLASS_NAME, "global-nav"))
                    )
                    logger.debug("导航栏元素已找到")
                except TimeoutException:
                    logger.debug("未找到导航栏元素，尝试其他元素")
                    # 尝试其他可能的元素
                    try:
                        self.wait.until(
                            EC.presence_of_element_located((By.CLASS_NAME, "feed-identity-module"))
                        )
                        logger.debug("Feed身份模块元素已找到")
                    except TimeoutException:
                        logger.error("无法找到任何登录成功的标志元素")
                        return {"success": False, "status": "登录可能失败，无法找到登录成功的标志元素", "requires_action": False}
                
                # 检查是否在登录页面
                if "login" in self.driver.current_url:
                    logger.error("登录失败：仍在登录页面")
                    return {"success": False, "status": "登录失败，请检查账号密码是否正确", "requires_action": False}
                
                # 检查是否成功加载导航栏
                if not self.driver.find_elements(By.CLASS_NAME, "global-nav") and not self.driver.find_elements(By.CLASS_NAME, "feed-identity-module"):
                    logger.error("登录失败：无法加载导航栏或Feed身份模块")
                    return {"success": False, "status": "登录失败，页面加载异常", "requires_action": False}
                
                self.is_logged_in = True
                logger.info("LinkedIn登录成功")
                return {"success": True, "status": "登录成功", "requires_action": False}
                
            except TimeoutException:
                logger.error("登录超时：无法加载主页")
                return {"success": False, "status": "登录超时，请检查网络连接", "requires_action": False}
            except Exception as e:
                logger.error(f"登录验证失败: {str(e)}")
                return {"success": False, "status": "登录验证失败，请稍后重试", "requires_action": False}
                
        except Exception as e:
            logger.error(f"登录过程发生未知错误: {str(e)}")
            return {"success": False, "status": "登录过程发生未知错误，请稍后重试", "requires_action": False}

    def verify_login_status(self) -> Dict:
        """手动验证登录状态

        Returns:
            包含验证结果的字典
        """
        try:
            logger.info("开始验证登录状态...")

            # 检查当前URL
            current_url = self.driver.current_url
            logger.debug(f"当前URL: {current_url}")

            # 如果在登录页面，说明未登录
            if "login" in current_url:
                logger.info("当前在登录页面，未登录")
                self.is_logged_in = False
                return {"success": False, "status": "未登录", "requires_action": False}

            # 检查是否需要验证
            if "challenge" in current_url or "checkpoint" in current_url:
                logger.warning("检测到需要验证")
                return {"success": False, "status": "需要完成验证，请在浏览器中完成验证", "requires_action": True}

            # 尝试访问LinkedIn主页来验证登录状态
            try:
                logger.debug("访问LinkedIn主页验证登录状态...")
                self.driver.get("https://www.linkedin.com/feed/")
                time.sleep(3)

                # 检查是否被重定向到登录页面
                if "login" in self.driver.current_url:
                    logger.info("被重定向到登录页面，登录已过期")
                    self.is_logged_in = False
                    return {"success": False, "status": "登录已过期，请重新登录", "requires_action": False}

                # 检查是否有导航栏或其他登录标志
                try:
                    # 等待导航栏加载
                    self.wait.until(
                        EC.any_of(
                            EC.presence_of_element_located((By.CLASS_NAME, "global-nav")),
                            EC.presence_of_element_located((By.CLASS_NAME, "feed-identity-module")),
                            EC.presence_of_element_located((By.CSS_SELECTOR, "[data-control-name='nav.settings']"))
                        )
                    )

                    logger.info("登录状态验证成功")
                    self.is_logged_in = True
                    return {"success": True, "status": "已登录", "requires_action": False}

                except TimeoutException:
                    logger.warning("无法找到登录标志元素")
                    # 再次检查URL
                    if "feed" in self.driver.current_url or "linkedin.com" in self.driver.current_url:
                        # 如果在LinkedIn域名下但找不到标志元素，可能是页面加载问题
                        logger.info("在LinkedIn域名下，假设已登录")
                        self.is_logged_in = True
                        return {"success": True, "status": "已登录（部分验证）", "requires_action": False}
                    else:
                        logger.error("登录状态验证失败")
                        self.is_logged_in = False
                        return {"success": False, "status": "登录状态验证失败", "requires_action": False}

            except Exception as e:
                logger.error(f"访问LinkedIn主页失败: {str(e)}")
                return {"success": False, "status": "无法访问LinkedIn，请检查网络连接", "requires_action": False}

        except Exception as e:
            logger.error(f"验证登录状态时发生错误: {str(e)}")
            return {"success": False, "status": "验证登录状态时发生错误", "requires_action": False}

    def search_jobs(self, keywords: str = None, location: str = None,
                   easy_apply_only: bool = True) -> List[Dict]:
        """搜索职位"""
        try:
            if not self.is_logged_in:
                logger.error("请先登录LinkedIn")
                return []

            if not keywords:
                keywords = self.config['linkedin']['search_keywords'][0]
            if not location:
                location = self.config['linkedin']['location']

            logger.info(f"搜索职位: {keywords} in {location}")

            # 构建搜索URL
            search_url = f"https://www.linkedin.com/jobs/search/?keywords={keywords}&location={location}"
            if easy_apply_only:
                search_url += "&f_AL=true"  # Easy Apply筛选

            self.driver.get(search_url)
            time.sleep(3)

            # 保存页面HTML快照到 log 文件夹
            log_dir = Path(__file__).parent.parent / "log"
            log_dir.mkdir(parents=True, exist_ok=True)
            html_file_path = log_dir / "linkedin_jobs_full_page.html"
            with open(html_file_path, "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            logger.info(f"页面HTML快照已保存到: {html_file_path}")

            # 等待职位列表加载
            job_cards = self.wait.until(
                EC.presence_of_all_elements_located(
                    (By.CSS_SELECTOR, ".job-card-container, .jobs-search-results__list-item")
                )
            )

            jobs = []
            for card in job_cards[:20]:  # 限制前20个职位
                try:
                    job_info = self._extract_job_info(card)
                    if job_info:
                        jobs.append(job_info)
                except Exception as e:
                    logger.warning(f"提取职位信息失败: {str(e)}", exc_info=True)
                    continue

            logger.info(f"找到 {len(jobs)} 个职位")
            return jobs

        except Exception as e:
            logger.error(f"搜索职位失败: {str(e)}", exc_info=True)
            # 保存失败时的页面HTML快照
            with open("linkedin_jobs_error_page.html", "w", encoding="utf-8") as f:
                f.write(self.driver.page_source)
            return []
    
    def _extract_job_info(self, job_card) -> Optional[Dict]:
        """从职位卡片提取信息"""
        try:
            # 捕获职位卡片的HTML内容
            logger.debug(f"职位卡片HTML: {job_card.get_attribute('outerHTML')}")

            # 职位标题
            title_element = job_card.find_element(
                By.CSS_SELECTOR, 
                ".job-card-list__title, .jobs-unified-top-card__job-title a, h3 a"
            )
            title = title_element.text.strip()
            job_url = title_element.get_attribute('href')

            # 公司名称
            company_element = job_card.find_element(
                By.CSS_SELECTOR,
                ".job-card-container__company-name, .jobs-unified-top-card__company-name a, .job-card-container__primary-description"
            )
            company = company_element.text.strip()

            # 地点
            try:
                location_element = job_card.find_element(
                    By.CSS_SELECTOR,
                    ".job-card-container__metadata-item, .jobs-unified-top-card__bullet"
                )
                location = location_element.text.strip()
            except:
                location = "未知地点"

            # 检查是否为Easy Apply
            is_easy_apply = False
            try:
                easy_apply_element = job_card.find_element(
                    By.CSS_SELECTOR,
                    ".jobs-apply-button--top-card, .job-card-container__apply-method"
                )
                if "Easy Apply" in easy_apply_element.text or "轻松申请" in easy_apply_element.text:
                    is_easy_apply = True
            except:
                pass

            return {
                'title': title,
                'company': company,
                'location': location,
                'url': job_url,
                'is_easy_apply': is_easy_apply,
                'job_id': self._extract_job_id(job_url)
            }

        except Exception as e:
            logger.warning(f"提取职位信息失败: {str(e)}")
            return None
    
    def _extract_job_id(self, job_url: str) -> str:
        """从URL提取职位ID"""
        try:
            if '/jobs/view/' in job_url:
                return job_url.split('/jobs/view/')[1].split('/')[0]
            return job_url.split('/')[-1].split('?')[0]
        except:
            return str(hash(job_url))
    
    def apply_to_job(self, job_info: Dict) -> bool:
        """申请职位"""
        try:
            job_id = job_info['job_id']
            
            # 检查是否已申请
            if job_id in self.applied_jobs:
                logger.info(f"职位 {job_info['title']} 已申请过，跳过")
                return False
            
            if not job_info['is_easy_apply']:
                logger.info(f"职位 {job_info['title']} 不支持Easy Apply，跳过")
                return False
            
            logger.info(f"开始申请职位: {job_info['title']} at {job_info['company']}")
            
            # 打开职位页面
            self.driver.get(job_info['url'])
            time.sleep(2)
            
            # 查找并点击Easy Apply按钮
            try:
                easy_apply_button = self.wait.until(
                    EC.element_to_be_clickable(
                        (By.XPATH, "//button[contains(@class, 'jobs-apply-button') and contains(., 'Easy Apply')]")
                    )
                )
                easy_apply_button.click()
                time.sleep(2)
                
            except TimeoutException:
                logger.warning(f"未找到Easy Apply按钮: {job_info['title']}")
                return False
            
            # 处理申请流程
            success = self._handle_application_process()
            
            if success:
                self.applied_jobs.add(job_id)
                logger.info(f"成功申请职位: {job_info['title']}")
                
                # 随机延迟
                delay = random.randint(*self.config['linkedin']['delay_between_applications'])
                logger.info(f"等待 {delay} 秒后继续...")
                time.sleep(delay)
                
            return success
            
        except Exception as e:
            logger.error(f"申请职位失败 {job_info['title']}: {str(e)}")
            return False
    
    def _handle_application_process(self) -> bool:
        """处理申请流程"""
        try:
            max_steps = 5
            current_step = 0
            
            while current_step < max_steps:
                time.sleep(2)
                
                # 检查是否有问题需要回答
                if self._answer_application_questions():
                    current_step += 1
                    continue
                
                # 查找下一步按钮
                next_button = None
                for selector in [
                    "//button[@aria-label='Continue to next step']",
                    "//button[contains(@class, 'artdeco-button--primary') and contains(., 'Next')]",
                    "//button[contains(@class, 'artdeco-button--primary') and contains(., '下一步')]"
                ]:
                    try:
                        next_button = self.driver.find_element(By.XPATH, selector)
                        break
                    except NoSuchElementException:
                        continue
                
                # 查找提交按钮
                submit_button = None
                for selector in [
                    "//button[@aria-label='Submit application']",
                    "//button[contains(@class, 'artdeco-button--primary') and contains(., 'Submit')]",
                    "//button[contains(@class, 'artdeco-button--primary') and contains(., '提交')]"
                ]:
                    try:
                        submit_button = self.driver.find_element(By.XPATH, selector)
                        break
                    except NoSuchElementException:
                        continue
                
                if submit_button and submit_button.is_enabled():
                    submit_button.click()
                    time.sleep(3)
                    
                    # 检查是否申请成功
                    try:
                        success_message = self.driver.find_element(
                            By.XPATH, 
                            "//*[contains(text(), 'Application sent') or contains(text(), '申请已发送')]"
                        )
                        return True
                    except NoSuchElementException:
                        pass
                    
                    return True
                    
                elif next_button and next_button.is_enabled():
                    next_button.click()
                    current_step += 1
                    time.sleep(2)
                    
                else:
                    # 检查是否有错误或需要手动处理
                    logger.warning("申请流程可能需要手动处理")
                    break
            
            return False
            
        except Exception as e:
            logger.error(f"处理申请流程失败: {str(e)}")
            return False
    
    def _answer_application_questions(self) -> bool:
        """回答申请问题"""
        try:
            answered = False
            
            # 查找文本输入框
            text_inputs = self.driver.find_elements(
                By.CSS_SELECTOR, 
                "input[type='text'], input[type='number'], textarea"
            )
            
            for input_field in text_inputs:
                if input_field.is_displayed() and input_field.is_enabled():
                    placeholder = input_field.get_attribute('placeholder') or ''
                    label = self._get_field_label(input_field)
                    
                    answer = self._get_answer_for_question(placeholder + ' ' + label)
                    if answer and not input_field.get_attribute('value'):
                        input_field.clear()
                        self._human_type(input_field, answer)
                        answered = True
            
            # 查找单选按钮
            radio_groups = self.driver.find_elements(
                By.CSS_SELECTOR,
                "fieldset, .fb-radio-buttons"
            )
            
            for group in radio_groups:
                if group.is_displayed():
                    radios = group.find_elements(By.CSS_SELECTOR, "input[type='radio']")
                    if radios and not any(radio.is_selected() for radio in radios):
                        # 选择第一个选项（通常是"是"或积极的回答）
                        radios[0].click()
                        answered = True
            
            # 查找下拉选择框
            selects = self.driver.find_elements(By.CSS_SELECTOR, "select")
            for select in selects:
                if select.is_displayed() and select.is_enabled():
                    options = select.find_elements(By.CSS_SELECTOR, "option")
                    if len(options) > 1 and not select.get_attribute('value'):
                        # 选择第二个选项（跳过默认的空选项）
                        options[1].click()
                        answered = True
            
            return answered
            
        except Exception as e:
            logger.warning(f"回答申请问题失败: {str(e)}")
            return False
    
    def _get_field_label(self, input_field) -> str:
        """获取输入框的标签"""
        try:
            # 尝试多种方式获取标签
            label_id = input_field.get_attribute('aria-labelledby')
            if label_id:
                label_element = self.driver.find_element(By.ID, label_id)
                return label_element.text
            
            # 查找相邻的label元素
            parent = input_field.find_element(By.XPATH, '..')
            label = parent.find_element(By.CSS_SELECTOR, 'label')
            return label.text
            
        except:
            return ''
    
    def _get_answer_for_question(self, question_text: str) -> str:
        """根据问题文本获取答案"""
        question_lower = question_text.lower()
        default_answers = self.config['linkedin']['default_answers']
        
        if 'experience' in question_lower or '经验' in question_lower:
            return default_answers.get('years_experience', '3')
        elif 'relocate' in question_lower or '搬迁' in question_lower:
            return default_answers.get('willing_to_relocate', 'Yes')
        elif 'authorized' in question_lower or 'work authorization' in question_lower:
            return default_answers.get('authorized_to_work', 'Yes')
        elif 'sponsorship' in question_lower or '赞助' in question_lower:
            return default_answers.get('require_sponsorship', 'No')
        elif 'phone' in question_lower or '电话' in question_lower:
            return '1234567890'  # 示例电话号码
        
        return ''
    
    def _human_type(self, element, text: str):
        """模拟人类打字"""
        for char in text:
            element.send_keys(char)
            time.sleep(random.uniform(0.05, 0.15))
    
    def batch_apply_jobs(self, max_applications: int = None) -> Dict:
        """批量申请职位"""
        if not max_applications:
            max_applications = self.config['linkedin']['max_applications_per_day']
        
        results = {
            'total_found': 0,
            'total_applied': 0,
            'successful_applications': [],
            'failed_applications': []
        }
        
        try:
            # 搜索职位
            for keyword in self.config['linkedin']['search_keywords']:
                if results['total_applied'] >= max_applications:
                    break
                    
                jobs = self.search_jobs(keyword)
                results['total_found'] += len(jobs)
                
                for job in jobs:
                    if results['total_applied'] >= max_applications:
                        break
                    
                    if self.apply_to_job(job):
                        results['total_applied'] += 1
                        results['successful_applications'].append(job)
                    else:
                        results['failed_applications'].append(job)
            
            logger.info(f"批量申请完成: 找到 {results['total_found']} 个职位，成功申请 {results['total_applied']} 个")
            return results
            
        except Exception as e:
            logger.error(f"批量申请失败: {str(e)}")
            return results
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            logger.info("浏览器已关闭")
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


# 使用示例
if __name__ == "__main__":
    # 创建配置文件示例
    config = {
        'linkedin': {
            'email': '<EMAIL>',
            'password': 'your_password',
            'search_keywords': ['python developer', 'software engineer', 'data scientist'],
            'location': 'United States',
            'max_applications_per_day': 20,
            'delay_between_applications': [30, 60],
            'auto_answer_questions': True,
            'default_answers': {
                'years_experience': '3',
                'willing_to_relocate': 'Yes',
                'authorized_to_work': 'Yes',
                'require_sponsorship': 'No'
            }
        },
        'selenium': {
            'headless': False,
            'implicit_wait': 10,
            'page_load_timeout': 30
        }
    }
    
    # 保存配置文件
    with open('linkedin_config.yaml', 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    # 使用自动化工具
    with LinkedInAutomation('linkedin_config.yaml') as linkedin:
        linkedin.setup_driver()
        
        if linkedin.login():
            results = linkedin.batch_apply_jobs(max_applications=10)
            print(f"申请结果: {results}")