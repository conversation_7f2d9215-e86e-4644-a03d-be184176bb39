#!/usr/bin/env python3
"""
LinkedIn自动化Web UI启动脚本
"""

import sys
import os
import subprocess
import time
from pathlib import Path

def check_dependencies():
    """检查必要的依赖是否已安装"""
    required_packages = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("playwright", "Playwright"),
        ("bs4", "BeautifulSoup4"),
        ("loguru", "Loguru")
    ]
    
    missing = []
    for package, name in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(name)
    
    if missing:
        print(f"❌ 缺失依赖: {', '.join(missing)}")
        print("请先运行: python install_linkedin_deps.py")
        return False
    
    print("✅ 所有依赖都已安装")
    return True

def check_config_files():
    """检查配置文件是否存在"""
    project_root = Path(__file__).parent
    
    # 检查LinkedIn配置文件
    linkedin_config = project_root / "linkedin_config.yaml"
    if not linkedin_config.exists():
        print("⚠️ 未找到 linkedin_config.yaml，创建默认配置...")
        create_default_linkedin_config(linkedin_config)
    
    # 检查数据文件夹
    data_folder = project_root / "data_folder"
    if not data_folder.exists():
        print("⚠️ 未找到 data_folder，创建默认数据文件夹...")
        create_default_data_folder(data_folder)
    
    return True

def create_default_linkedin_config(config_path):
    """创建默认的LinkedIn配置文件"""
    import yaml
    
    config = {
        'linkedin': {
            'email': '',  # 用户需要填写
            'password': '',  # 用户需要填写
            'search_keywords': ['python developer', 'software engineer'],
            'location': 'United States',
            'experience_level': ['Entry level', 'Associate', 'Mid-Senior level'],
            'job_type': ['Full-time', 'Part-time', 'Contract'],
            'remote_work': ['On-site', 'Remote', 'Hybrid'],
            'max_applications_per_day': 50,
            'delay_between_applications': [30, 60],
            'auto_answer_questions': True,
            'default_answers': {
                'years_experience': '3',
                'willing_to_relocate': 'Yes',
                'authorized_to_work': 'Yes',
                'require_sponsorship': 'No'
            }
        },
        'playwright': {
            'headless': False,
            'timeout': 30000,
            'window_size': [1200, 800]
        }
    }
    
    with open(config_path, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ 已创建默认配置文件: {config_path}")

def create_default_data_folder(data_folder):
    """创建默认的数据文件夹和文件"""
    import yaml
    
    data_folder.mkdir(exist_ok=True)
    
    # 创建secrets.yaml
    secrets_file = data_folder / "secrets.yaml"
    if not secrets_file.exists():
        secrets = {
            'llm_api_key': 'your_api_key_here'  # 用户需要填写
        }
        with open(secrets_file, 'w', encoding='utf-8') as f:
            yaml.dump(secrets, f, default_flow_style=False, allow_unicode=True)
        print(f"✅ 已创建 {secrets_file}")
    
    # 创建work_preferences.yaml
    work_prefs_file = data_folder / "work_preferences.yaml"
    if not work_prefs_file.exists():
        work_prefs = {
            'remote': True,
            'experience_level': {
                'internship': False,
                'entry': True,
                'associate': True,
                'mid_senior_level': True,
                'director': False,
                'executive': False
            },
            'job_types': {
                'full_time': True,
                'contract': False,
                'part_time': False,
                'temporary': False,
                'internship': False,
                'other': False,
                'volunteer': False
            },
            'date': {
                'all_time': False,
                'month': True,
                'week': False,
                '24_hours': False
            },
            'positions': ['Software Engineer', 'Python Developer'],
            'locations': ['United States'],
            'location_blacklist': [],
            'distance': 25,
            'company_blacklist': [],
            'title_blacklist': []
        }
        with open(work_prefs_file, 'w', encoding='utf-8') as f:
            yaml.dump(work_prefs, f, default_flow_style=False, allow_unicode=True)
        print(f"✅ 已创建 {work_prefs_file}")
    
    # 创建plain_text_resume.yaml
    resume_file = data_folder / "plain_text_resume.yaml"
    if not resume_file.exists():
        resume_content = """personal_information:
  name: "Your Name"
  surname: "Your Surname"
  date_of_birth: "01/01/1990"
  country: "United States"
  city: "Your City"
  address: "Your Address"
  phone_prefix: "+1"
  phone: "1234567890"
  email: "<EMAIL>"
  github: "https://github.com/yourusername"
  linkedin: "https://linkedin.com/in/yourusername"

education_details:
  - degree: "Bachelor of Science"
    university: "Your University"
    gpa: "3.8/4.0"
    graduation_year: "2020"
    field_of_study: "Computer Science"

experience_details:
  - position: "Software Developer"
    company: "Your Company"
    employment_period: "2020-Present"
    location: "Your City, State"
    industry: "Technology"
    key_responsibilities:
      - "Developed web applications using Python and JavaScript"
      - "Collaborated with cross-functional teams"
      - "Implemented automated testing procedures"
    skills_acquired:
      - "Python"
      - "JavaScript"
      - "SQL"

projects:
  - name: "Project Name"
    description: "Brief description of your project"
    link: "https://github.com/yourusername/project"

achievements:
  - name: "Achievement Name"
    description: "Description of your achievement"

certifications:
  - name: "Certification Name"
    description: "Description of certification"

languages:
  - language: "English"
    proficiency: "Native"

interests:
  - "Software Development"
  - "Machine Learning"
  - "Open Source"
"""
        with open(resume_file, 'w', encoding='utf-8') as f:
            f.write(resume_content)
        print(f"✅ 已创建 {resume_file}")
    
    # 创建output文件夹
    output_folder = data_folder / "output"
    output_folder.mkdir(exist_ok=True)

def start_backend():
    """启动后端服务"""
    backend_dir = Path(__file__).parent / "webui" / "backend"
    
    if not backend_dir.exists():
        print(f"❌ 未找到后端目录: {backend_dir}")
        return False
    
    print("🚀 启动后端服务...")
    print(f"工作目录: {backend_dir}")
    
    try:
        # 切换到后端目录并启动服务
        os.chdir(backend_dir)
        subprocess.run([
            sys.executable, "-m", "uvicorn", "main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], check=True)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("LinkedIn自动化Web UI启动器")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查配置文件
    if not check_config_files():
        return
    
    print("\n📋 启动前提醒:")
    print("1. 请在 linkedin_config.yaml 中配置您的LinkedIn账号")
    print("2. 请在 data_folder/secrets.yaml 中配置API密钥")
    print("3. 请在 data_folder/plain_text_resume.yaml 中配置您的简历信息")
    print("4. Web UI将在 http://localhost:8000 启动")
    
    response = input("\n是否继续启动？(Y/n): ").strip().lower()
    if response == 'n':
        print("启动已取消")
        return
    
    # 启动后端
    start_backend()

if __name__ == "__main__":
    main()
