# Jobs Application LinkedIn AIHawk

🤖 **智能LinkedIn求职助手** - 基于AI的自动化求职申请系统

[![GitHub stars](https://img.shields.io/github/stars/YourUsername/Jobs-Application_Linkedin_AIHawk.svg)](https://github.com/YourUsername/Jobs-Application_Linkedin_AIHawk/stargazers)
[![GitHub forks](https://img.shields.io/github/forks/YourUsername/Jobs-Application_Linkedin_AIHawk.svg)](https://github.com/YourUsername/Jobs-Application_Linkedin_AIHawk/network)
[![GitHub issues](https://img.shields.io/github/issues/YourUsername/Jobs-Application_Linkedin_AIHawk.svg)](https://github.com/YourUsername/Jobs-Application_Linkedin_AIHawk/issues)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 🌟 功能特色

### 📋 **简历智能优化**
- **PDF简历上传**：支持各种格式的简历解析和智能提取
- **AI智能优化**：基于Gemini 2.5 Flash的深度简历优化
- **职位匹配**：根据目标职位自动调整简历内容和关键词
- **实时高亮**：AI优化内容实时高亮显示，支持一键切换
- **HTML预览**：实时预览优化后的简历效果，支持新窗口查看
- **PDF生成**：一键生成专业格式的PDF简历
- **多语言支持**：支持中英文简历优化和生成

### 💌 **求职信生成**
- **智能生成**：基于简历和职位信息自动生成个性化求职信
- **双语支持**：支持中英文双语求职信生成
- **公司Logo**：自动抓取目标公司Logo并集成到求职信
- **专业模板**：现代化设计模板，支持HTML和PDF导出
- **个性化内容**：根据职位要求和个人经历定制内容

### 🔍 **LinkedIn自动化**
- **职位搜索**：自动搜索LinkedIn职位信息
- **智能筛选**：根据条件筛选合适的职位
- **自动申请**：支持Easy Apply自动申请
- **多种模式**：支持Selenium和Playwright两种自动化方式
- **2FA支持**：支持SMS二次验证自动处理

### 🎯 **智能匹配**
- **职位解析**：智能解析职位要求和描述
- **技能匹配**：自动匹配用户技能与职位需求
- **个性化优化**：为每个职位生成定制化简历
- **匹配度分析**：显示简历与职位的匹配度评分

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- Chrome浏览器
- Gemini API密钥

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/YourUsername/Jobs-Application_Linkedin_AIHawk.git
cd Jobs-Application_Linkedin_AIHawk
```

2. **安装Python依赖**
```bash
python -m venv virtual
virtual\Scripts\activate  # Windows
# source virtual/bin/activate  # Linux/Mac
pip install -r requirements.txt
```

3. **安装前端依赖**
```bash
cd webui/frontend
npm install
cd ../..
```

4. **配置API密钥**
```bash
# 复制配置文件
cp config.py.example config.py
cp secrets.yaml.example secrets.yaml

# 编辑secrets.yaml，添加您的Gemini API密钥
# gemini_api_key: "your-gemini-api-key-here"
```

### 启动服务

1. **启动后端服务**
```bash
cd webui/backend
python main.py
```
后端服务将在 `http://localhost:8003` 启动

2. **启动前端服务**
```bash
cd webui/frontend
npm start
```
前端服务将在 `http://localhost:3000` 启动

3. **访问应用**
打开浏览器访问: `http://localhost:3000`

## 📖 使用指南

### 简历优化流程

1. **上传简历**：在Web界面上传您的PDF简历文件
2. **输入职位信息**：粘贴LinkedIn职位URL
3. **AI优化**：系统自动分析职位要求并优化您的简历
4. **预览效果**：查看AI优化的内容（带✨高亮标记）
5. **切换显示**：使用"隐藏高亮"按钮切换优化内容的显示
6. **下载简历**：生成并下载优化后的PDF简历

### 求职信生成

1. **完成简历优化**：首先完成简历优化流程
2. **切换到求职信页面**：点击"求职信生成"标签
3. **生成求职信**：系统自动生成个性化求职信
4. **预览和下载**：查看HTML预览并下载PDF版本

### LinkedIn自动化

1. **配置账户**：在配置文件中设置LinkedIn账户信息
2. **设置搜索条件**：配置职位搜索关键词和筛选条件
3. **启动自动化**：运行自动化脚本开始申请职位
4. **监控进度**：查看申请日志和结果统计

## 📖 使用指南

### 简历优化流程

1. **上传简历**：在Web界面上传您的PDF简历
2. **输入职位信息**：粘贴LinkedIn职位URL或手动输入职位描述
3. **AI优化**：系统自动分析并优化您的简历
4. **预览和调整**：在HTML预览中查看优化效果
5. **下载PDF**：生成并下载优化后的简历

### LinkedIn自动化

1. **配置账户**：在配置文件中设置LinkedIn账户信息
2. **设置搜索条件**：配置职位搜索关键词和筛选条件
3. **启动自动化**：运行自动化脚本开始申请职位
4. **监控进度**：查看申请日志和结果统计

## ⚙️ 配置说明

### API配置
- **Gemini API**：用于AI简历优化和求职信生成
  - 支持Gemini 2.5 Flash Preview模型
  - 温度设置为1.0以获得更好的创意输出
- **LinkedIn API**：用于职位信息获取和自动化

### 自动化配置
- **搜索关键词**：设置职位搜索关键词
- **地理位置**：设置目标工作地点
- **申请限制**：设置每日申请数量限制
- **浏览器选择**：支持Selenium和Playwright两种模式

## 🛠️ 技术栈

### 后端
- **Python 3.8+**
- **FastAPI**：现代化Web API框架
- **Selenium/Playwright**：浏览器自动化工具
- **Google Gemini 2.5**：最新AI语言模型
- **PyPDF2**：PDF文件处理
- **BeautifulSoup4**：HTML解析

### 前端
- **React 18**：现代化前端框架
- **Material-UI (MUI)**：专业UI组件库
- **Vite**：快速构建工具
- **Axios**：HTTP客户端

### AI & 机器学习
- **Google Gemini API**：自然语言处理
- **智能简历优化**：基于职位要求的内容优化
- **语言检测**：自动识别简历语言

### 其他工具
- **Chrome WebDriver**：浏览器自动化驱动
- **HTML to PDF**：文档格式转换
- **CORS处理**：跨域请求支持

## 📁 项目结构

```
Jobs-Application_Linkedin_AIHawk/
├── src/                          # 核心源代码
│   ├── libs/                     # 核心库
│   │   ├── resume_and_cover_builder/  # 简历和求职信生成器
│   │   │   ├── llm/              # LLM相关模块
│   │   │   │   ├── llm_job_parser.py      # 职位解析
│   │   │   │   ├── llm_generate_resume.py # 简历生成
│   │   │   │   └── llm_generate_tailored_resume.py # 定制简历
│   │   │   ├── resume_style/     # 简历样式模板
│   │   │   └── resume_facade.py  # 简历生成门面
│   │   └── llm_manager.py        # LLM管理器
│   ├── utils/                    # 工具模块
│   │   └── chrome_utils.py       # Chrome浏览器工具
│   └── linkedin_automation_*.py  # LinkedIn自动化脚本
├── webui/                        # Web用户界面
│   ├── backend/                  # 后端API服务
│   │   ├── main.py              # FastAPI主服务
│   │   └── linkedin_api.py      # LinkedIn API接口
│   └── frontend/                 # React前端应用
│       ├── src/                 # 前端源代码
│       │   ├── App.jsx          # 主应用组件
│       │   └── index.js         # 入口文件
│       ├── package.json         # 前端依赖配置
│       └── vite.config.js       # Vite构建配置
├── config.py                     # 主配置文件
├── secrets.yaml                  # API密钥配置
├── requirements.txt              # Python依赖列表
└── README.md                     # 项目说明文档
```

## 🎯 最新更新

### v2.0.0 (2024-12-21)
- ✨ **新增求职信生成功能**：智能生成个性化求职信
- 🔧 **优化简历格式**：英文简历支持项目符号列表格式
- 🎨 **改进用户界面**：修复按钮重复问题，优化高亮显示
- 🚀 **升级AI模型**：使用Gemini 2.5 Flash Preview获得更好效果
- 🌐 **增强多语言支持**：完善中英文双语功能
- 📱 **响应式设计**：优化移动端显示效果

## 🔧 故障排除

### 常见问题

**Q: Gemini API调用失败**
A: 请检查API密钥是否正确配置在`secrets.yaml`文件中，确保网络连接正常。

**Q: 前端无法连接后端**
A: 确保后端服务在8003端口正常运行，检查防火墙设置。

**Q: PDF生成失败**
A: 确保Chrome浏览器已正确安装，检查系统权限设置。

**Q: LinkedIn自动化不工作**
A: 检查LinkedIn账户是否正常，确保没有触发反机器人检测。

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 开发指南
- 遵循Python PEP 8代码规范
- 前端使用ESLint和Prettier格式化
- 提交前请运行测试用例
- 添加新功能时请更新文档

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本工具仅供学习和研究使用。使用时请遵守LinkedIn的服务条款和相关法律法规。自动化功能请谨慎使用，避免违反平台规则。

## 🙏 致谢

- [AIHawk](https://github.com/feder-cr/AIHawk) - 原始项目灵感来源
- [Google Gemini](https://ai.google.dev/) - 强大的AI语言模型支持
- [Material-UI](https://mui.com/) - 优秀的React UI组件库
- [FastAPI](https://fastapi.tiangolo.com/) - 现代化的Python Web框架
- [React](https://reactjs.org/) - 强大的前端框架

## 📞 联系方式

如有问题或建议，欢迎通过以下方式联系：
- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [提交问题](https://github.com/YourUsername/Jobs-Application_Linkedin_AIHawk/issues)
- 🐦 Twitter: @YourTwitterHandle

---

⭐ **如果这个项目对您有帮助，请给个Star支持一下！** ⭐

🚀 **让AI助力您的求职之路，祝您早日找到理想工作！** 🚀
